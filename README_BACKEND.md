# Backend de AI Hedge Fund

## Ejecutar el servidor

Para ejecutar el servidor backend correctamente, debes hacerlo desde la raíz del proyecto para que las importaciones absolutas funcionen correctamente:

```bash
# Desde la raíz del proyecto
poetry run python app/backend/run_server.py
```

Esto iniciará el servidor FastAPI en http://127.0.0.1:8000

## Estructura del backend

El backend está organizado de la siguiente manera:

```
app/
└── backend/
    ├── main.py           # Punto de entrada principal de FastAPI
    ├── run_server.py     # Script para ejecutar el servidor desde la raíz
    ├── routes/           # Definiciones de rutas API
    │   ├── __init__.py   # Configuración del router principal
    │   ├── health.py     # Endpoints de salud/estado
    │   └── hedge_fund.py # Endpoints específicos del hedge fund
    └── ...               # <PERSON><PERSON><PERSON> módulos (modelos, servicios, etc.)
```

## Solución de problemas

Si encuentras el error `ModuleNotFoundError: No module named 'app'`, asegúrate de estar ejecutando el servidor desde la raíz del proyecto utilizando el script `run_server.py`.

**Incorrecto:**
```bash
# Desde app/backend
poetry run uvicorn main:app --reload  # ❌ Esto fallará
```

**Correcto:**
```bash
# Desde la raíz del proyecto
poetry run python app/backend/run_server.py  # ✅ Esto funcionará
```

El script `run_server.py` configura correctamente el path de Python para que las importaciones absolutas como `from app.backend.routes import api_router` funcionen correctamente.
