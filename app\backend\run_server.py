"""
Script para ejecutar el servidor FastAPI desde la raíz del proyecto.
Esto asegura que las importaciones absolutas funcionen correctamente.
"""
import os
import sys
import uvicorn

# Añadir la raíz del proyecto al path de Python
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

if __name__ == "__main__":
    # Ejecutar el servidor uvicorn
    uvicorn.run("app.backend.main:app", host="127.0.0.1", port=8000, reload=True)
